{"private": true, "scripts": {"dev": "next --port 3000", "build": "node ./scripts/gen-rss.js && next build", "start": "next start --port 3000", "docker:build": "docker build -t nextjs-blog-sample .", "docker:run": "docker run -p 3000:3000 nextjs-blog-sample"}, "dependencies": {"gray-matter": "^4.0.3", "next": "latest", "nextra": "^2.0.0-beta.5", "nextra-theme-blog": "^2.0.0-beta.5", "react": "^18.2.0", "react-dom": "^18.2.0", "rss": "^1.2.2"}, "devDependencies": {"@types/node": "^18.0.0", "@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "typescript": "^4.7.4"}}